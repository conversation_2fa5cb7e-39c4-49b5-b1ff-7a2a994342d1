"""
test_integrate_upgrade_one_stream.py

Unit testing for integrate_upgrade_one_stream
"""
import collections
import os
import unittest
from mock import call, MagicMock, patch
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dice_elipy_scripts.integrate_upgrade_one_stream import cli

FilePath = collections.namedtuple("FilePath", ["local_path", "depot_path"])


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.running_processes.kill", MagicMock())
@patch("dice_elipy_scripts.integrate_upgrade_one_stream.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.integrate_upgrade_one_stream.throw_if_files_found", MagicMock())
class TestIntegrateUpgradeOneStream(unittest.TestCase):
    OPTION_ASSETS = "--assets"
    OPTION_CHANGELIST = "--changelist"
    OPTION_CODE_CLEAN = "--code-clean"
    OPTION_COPY_MAPPING = "--copy-mapping"
    OPTION_COPY_REVERSE = "--copy-reverse"
    OPTION_DATA_CLEAN = "--data-clean"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_DATA_PLATFORM = "--data-platform"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_IGNORE_SOURCE_HISTORY = "--ignore-source-history"
    OPTION_INTEGRATE_MAPPING = "--integrate-mapping"
    OPTION_INTEGRATE_REVERSE = "--integrate-reverse"
    OPTION_INTEGRATION_UPGRADE_SCRIPT_PATH = "--integration-upgrade-script-path"
    OPTION_LAST_CHANGELIST = "--last-changelist"
    OPTION_LICENSEE = "--licensee"
    OPTION_LOCAL_UPGRADE = "--local-upgrade"
    OPTION_LOCAL_UPGRADE_SCRIPT_PATH = "--local-upgrade-script-path"
    OPTION_NO_SUBMIT = "--no-submit"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_P4_CLIENT_BRANCH_GUARDIAN_RULES_CLEANUP = "--p4-client-branch-guardian-rules-cleanup"
    OPTION_P4_CLEAN_FAILED_UPGRADE = "--p4-clean-failed-upgrade"
    OPTION_P4_PATH_SOURCE = "--p4-path-source"
    OPTION_P4_PATH_TARGET = "--p4-path-target"
    OPTION_P4_PORT = "--p4-port"
    OPTION_PIPELINE_ARGS = "--pipeline-args"
    OPTION_REVERT_BRANCHID_FILE = "--revert-branchid-file"
    OPTION_RUN_BRANCH_GUARDIAN = "--run-branch-guardian"
    OPTION_RUN_COOK = "--run-cook"
    OPTION_RUN_UPGRADE = "--run-upgrade"
    OPTION_SHELVE_CL = "--shelve-cl"
    OPTION_USE_PREVIEW_DOTNET_VERSION = "--use-preview-dotnet-version"

    VALUE_ASSETS = "test_asset"
    VALUE_CHANGELIST = "6543"
    VALUE_CODE_CLEAN = "true"
    VALUE_COPY_MAPPING = "copy_mapping"
    VALUE_DATA_CLEAN = "true"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_DATA_PLATFORM = "ps5"
    VALUE_FRAMEWORK_ARGS = "fw_arg"
    VALUE_INTEGRATE_MAPPING = "integrate_mapping"
    VALUE_INTEGRATION_UPGRADE_SCRIPT_PATH = "integration\\upgrade\\script_path"
    VALUE_INTEGRATION_UPGRADE_SCRIPT_PATH_DEFAULT = (
        "tnt_root\\Code\\DICE\\UpgradeScripts\\UpgradeScripts.txt"
    )
    VALUE_LAST_CHANGELIST = "5432"
    VALUE_LICENSEE_1 = "licensee_1"
    VALUE_LICENSEE_2 = "licensee_2"
    VALUE_LOCAL_UPGRADE_SCRIPT_PATH = "local\\upgrade\\script_path"
    VALUE_LOCAL_UPGRADE_SCRIPT_PATH_DEFAULT = (
        "tnt_root\\Code\\DICE\\UpgradeScripts\\UpgradeLocal.bat"
    )
    VALUE_P4_CLIENT = "p4_client"
    VALUE_P4_CLIENT_BRANCH_GUARDIAN_RULES_CLEANUP = "p4_client_bg_cleanup"
    VALUE_P4_PATH_SOURCE = "//depot/path/source"
    VALUE_P4_PATH_TARGET = "//depot/path/target"
    VALUE_P4_PORT = "p4_port"
    VALUE_PIPELINE_ARGS = "arg1"
    VALUE_RUN_BRANCH_GUARDIAN = "true"
    VALUE_RUN_COOK = "false"
    VALUE_RUN_UPGRADE = "false"
    VALUE_SHELVE_CL = "true"
    VALUE_USE_PREVIEW_DOTNET_VERSION = "true"

    FIXED_ARGS = [
        OPTION_ASSETS,
        VALUE_ASSETS,
        OPTION_CHANGELIST,
        VALUE_CHANGELIST,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_LICENSEE,
        VALUE_LICENSEE_1,
    ]

    BASIC_ARGS = FIXED_ARGS + [OPTION_INTEGRATE_MAPPING, VALUE_INTEGRATE_MAPPING]

    def setUp(self):
        self.patcher_run_frostbite_data_upgrade = patch(
            "elipy2.data.DataUtils.run_frostbite_data_upgrade"
        )
        self.mock_run_frostbite_data_upgrade = self.patcher_run_frostbite_data_upgrade.start()

        self.patcher_set_datadir = patch("elipy2.data.DataUtils.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value = MagicMock()
        self.mock_p4utils.return_value.changes_range.return_value = [6432, 6543]
        self.mock_p4utils.return_value.unresolved.return_value = []

        self.patcher_ensure_p4_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_p4_config = self.patcher_ensure_p4_config.start()

        self.patcher_run = patch("elipy2.core.run")
        self.mock_run = self.patcher_run.start()

        self.patcher_delete_folder = patch("elipy2.core.delete_folder")
        self.mock_delete_folder = self.patcher_delete_folder.start()

        self.patcher_get_game_data_dir = patch("elipy2.frostbite_core.get_game_data_dir")
        self.mock_get_game_data_dir = self.patcher_get_game_data_dir.start()
        self.mock_get_game_data_dir.return_value = "game_data_dir"

        self.patcher_get_tnt_local_path = patch("elipy2.local_paths.get_tnt_local_path")
        self.mock_get_tnt_local_path = self.patcher_get_tnt_local_path.start()
        self.mock_get_tnt_local_path.return_value = "tnt_local"

        self.patcher_compile_code = patch(
            "dice_elipy_scripts.integrate_upgrade_one_stream.compile_code"
        )
        self.mock_compile_code = self.patcher_compile_code.start()

        self.patcher_cook_data = patch("dice_elipy_scripts.integrate_upgrade_one_stream.cook_data")
        self.mock_cook_data = self.patcher_cook_data.start()

        self.patcher_submit_integration = patch(
            "dice_elipy_scripts.integrate_upgrade_one_stream.submit_integration"
        )
        self.mock_submit_integration = self.patcher_submit_integration.start()

        self.patcher_find_package = patch("elipy2.frostbite.package_utils.find_package")
        self.mock_find_package = self.patcher_find_package.start()
        self.mock_find_package.return_value = {
            "DotNetSdk": {
                "package": "DotNetSdk",
                "version": "8.0.201",
                "path": "C:\\packages\\DotNetSdk\\8.0.201_19773081",
                "uri": "p4://dice-p4-one.dice.ad.ea.com:2001/SDK/DotNetSdk/8.0.201?cl=19773081",
                "fragment": "C:\\P4\\trunk_code_dev\\TnT\\masterconfig.xml",
            }
        }

    def tearDown(self):
        patch.stopall()

    def test_basic_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 2

    def test_integration_unresolved(self):
        self.mock_p4utils.return_value.unresolved.return_value = [
            FilePath("local_path", "//depot/path")
        ]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 1
        self.mock_submit_integration.assert_not_called()
        assert self.mock_p4utils.return_value.revert.call_count == 2

    def test_compile_code_default(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE_1],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_P4_PORT,
            user=None,
            client=self.VALUE_P4_CLIENT,
            buildsln_framework_args=[],
            framework_args=[],
            clean=False,
        )

    def test_compile_code_clean(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_CODE_CLEAN, self.VALUE_CODE_CLEAN]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE_1],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_P4_PORT,
            user=None,
            client=self.VALUE_P4_CLIENT,
            buildsln_framework_args=[],
            framework_args=[],
            clean=True,
        )

    def test_compile_code_framework_args(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_FRAMEWORK_ARGS, self.VALUE_FRAMEWORK_ARGS]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE_1],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_P4_PORT,
            user=None,
            client=self.VALUE_P4_CLIENT,
            buildsln_framework_args=[],
            framework_args=[self.VALUE_FRAMEWORK_ARGS],
            clean=False,
        )

    @patch("json.loads")
    def test_compile_code_framework_args_branch_guardian(self, mock_json_loads):
        self.mock_run.side_effect = [
            (0, "some_output", "some_error"),
            (0, "some_output", "some_error"),
            (0, "some_output", "some_error"),
            (0, ["output1", "output2"], "some_error"),
        ]
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "P4INTEGRATE",
                        "Flags": "-Rbd",
                        "Arg1": "//bf/mainline/trunk-content-dev/bfdata/...",
                        "Arg2": "//bf/mainline/trunk-code-dev/bfdata/...",
                    },
                ]
            },
            {
                "ScriptCommands": [
                    {
                        "Type": "P4INTEGRATE",
                        "Flags": "-Rbd",
                        "Arg1": "//bf/mainline/trunk-content-dev/bfdata/...",
                        "Arg2": "//bf/mainline/trunk-code-dev/bfdata/...",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_FRAMEWORK_ARGS,
                self.VALUE_FRAMEWORK_ARGS,
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE_1],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_P4_PORT,
            user=None,
            client=self.VALUE_P4_CLIENT,
            buildsln_framework_args=[],
            framework_args=[self.VALUE_FRAMEWORK_ARGS],
            clean=False,
        )

    @patch("json.loads")
    def test_compile_code_framework_args_branch_guardian_use_preview(self, mock_json_loads):
        self.mock_run.side_effect = [
            (0, "some_output", "some_error"),
            (0, "some_output", "some_error"),
            (0, "some_output", "some_error"),
            (0, "some_output", "some_error"),
            (0, ["output1", "output2"], "some_error"),
        ]
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "P4INTEGRATE",
                        "Flags": "-Rbd",
                        "Arg1": "//bf/mainline/trunk-content-dev/bfdata/...",
                        "Arg2": "//bf/mainline/trunk-code-dev/bfdata/...",
                    },
                ]
            },
            {
                "ScriptCommands": [
                    {
                        "Type": "P4INTEGRATE",
                        "Flags": "-Rbd",
                        "Arg1": "//bf/mainline/trunk-content-dev/bfdata/...",
                        "Arg2": "//bf/mainline/trunk-code-dev/bfdata/...",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_FRAMEWORK_ARGS,
                self.VALUE_FRAMEWORK_ARGS,
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
                self.OPTION_USE_PREVIEW_DOTNET_VERSION,
                self.VALUE_USE_PREVIEW_DOTNET_VERSION,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE_1],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_P4_PORT,
            user=None,
            client=self.VALUE_P4_CLIENT,
            buildsln_framework_args=["-G:use.dotnet.preview.platform=windows"],
            framework_args=[self.VALUE_FRAMEWORK_ARGS, "-G:use.dotnet.preview.platform=windows"],
            clean=False,
        )

    def test_run_upgrade_single_licensee(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run_frostbite_data_upgrade.assert_called_once_with(
            source_game_data_dir=os.path.join("game_root", "SourceData"),
            dest_game_data_dir="game_data_dir",
            licensee=self.VALUE_LICENSEE_1,
            scripts_path=self.VALUE_INTEGRATION_UPGRADE_SCRIPT_PATH_DEFAULT,
            extra_fdu_args=None,
        )

    def test_run_upgrade_multiple_licensees(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_LICENSEE, self.VALUE_LICENSEE_2])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run_frostbite_data_upgrade.assert_called_once_with(
            source_game_data_dir=os.path.join("game_root", "SourceData"),
            dest_game_data_dir="game_data_dir",
            licensee=self.VALUE_LICENSEE_1,
            scripts_path=self.VALUE_INTEGRATION_UPGRADE_SCRIPT_PATH_DEFAULT,
            extra_fdu_args=None,
        )

    def test_run_upgrade_exception(self):
        self.mock_run_frostbite_data_upgrade.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        assert self.mock_p4utils.return_value.clean.call_count == 0
        self.mock_delete_folder.assert_called_once_with("tnt_local")

    def test_run_upgrade_exception_run_clean(self):
        self.mock_run_frostbite_data_upgrade.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_P4_CLEAN_FAILED_UPGRADE])
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        self.mock_p4utils.return_value.clean.assert_called_once_with(folder="game_data_dir/...")
        self.mock_delete_folder.assert_called_once_with("tnt_local")

    def test_run_upgrade_script_path(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_INTEGRATION_UPGRADE_SCRIPT_PATH,
                self.VALUE_INTEGRATION_UPGRADE_SCRIPT_PATH,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run_frostbite_data_upgrade.assert_called_once_with(
            source_game_data_dir=os.path.join("game_root", "SourceData"),
            dest_game_data_dir="game_data_dir",
            licensee=self.VALUE_LICENSEE_1,
            scripts_path=self.VALUE_INTEGRATION_UPGRADE_SCRIPT_PATH,
            extra_fdu_args=None,
        )

    def test_skip_upgrade(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_RUN_UPGRADE, self.VALUE_RUN_UPGRADE]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_run_frostbite_data_upgrade.call_count == 0

    @patch("json.loads")
    def test_branch_guardian_integrate(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "P4INTEGRATE",
                        "Flags": "-Rbd",
                        "Arg1": "//bf/mainline/trunk-content-dev/bfdata/...",
                        "Arg2": "//bf/mainline/trunk-code-dev/bfdata/...",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.integrate.call_count == 2
        self.mock_p4utils.return_value.integrate.assert_has_calls(
            [
                call(
                    mapping="integrate_mapping",
                    reverse=False,
                    to_revision="6543",
                    ignore_source_history=False,
                ),
                call(
                    mapping="//bf/mainline/trunk-content-dev/bfdata/...",
                    reverse=False,
                    stream=False,
                    to_revision=self.VALUE_CHANGELIST,
                    parent="//bf/mainline/trunk-code-dev/bfdata/...",
                    use_file_paths=True,
                    ignore_source_history=False,
                    resolve_mode=["b", "d"],
                ),
            ]
        )

    @patch("json.loads")
    def test_branch_guardian_resolve(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "P4RESOLVE",
                        "Flags": "-at",
                        "Arg1": "//bf/mainline/trunk-code-dev/bfdata/...",
                        "Arg2": "",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.resolve.assert_has_calls(
            [
                call(mode="m"),
                call(mode="t", path="//bf/mainline/trunk-code-dev/bfdata/..."),
            ]
        )

    @patch("json.loads")
    def test_branch_guardian_fdu(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "FDU",
                        "Flags": "/FULLUPGRADE /LICENSEE BattlefieldGame /GENERATETYPEDB /SOURCECONTROL /NOSOURCEMODULES /WRITE /SOURCECONTROLOVERWRITE",
                        "Arg1": "",
                        "Arg2": "",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run_frostbite_data_upgrade.assert_called_once_with(
            source_game_data_dir="game_data_dir",
            dest_game_data_dir="game_data_dir",
            licensee=self.VALUE_LICENSEE_1,
            scripts_path=self.VALUE_INTEGRATION_UPGRADE_SCRIPT_PATH_DEFAULT,
            extra_fdu_args=[
                "/FULLUPGRADE",
                "/LICENSEE",
                "BattlefieldGame",
                "/GENERATETYPEDB",
                "/SOURCECONTROL",
                "/NOSOURCEMODULES",
                "/WRITE",
                "/SOURCECONTROLOVERWRITE",
            ],
        )
        assert self.mock_p4utils.return_value.unresolved.call_count == 2

    @patch("json.loads")
    def test_branch_guardian_fdu_failure(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.return_value = {
            "ScriptCommands": [
                {
                    "Type": "FDU",
                    "Flags": "/FULLUPGRADE /LICENSEE BattlefieldGame /GENERATETYPEDB /SOURCECONTROL /NOSOURCEMODULES /WRITE /SOURCECONTROLOVERWRITE",
                    "Arg1": "",
                    "Arg2": "",
                },
            ]
        }
        self.mock_run_frostbite_data_upgrade.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        assert self.mock_p4utils.return_value.clean.call_count == 0
        self.mock_delete_folder.assert_called_once_with("tnt_local")

    @patch("json.loads")
    def test_branch_guardian_fdu_failure_run_clean(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.return_value = {
            "ScriptCommands": [
                {
                    "Type": "FDU",
                    "Flags": "/FULLUPGRADE /LICENSEE BattlefieldGame /GENERATETYPEDB /SOURCECONTROL /NOSOURCEMODULES /WRITE /SOURCECONTROLOVERWRITE",
                    "Arg1": "",
                    "Arg2": "",
                },
            ]
        }
        self.mock_run_frostbite_data_upgrade.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
                self.OPTION_P4_CLEAN_FAILED_UPGRADE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        self.mock_p4utils.return_value.clean.assert_called_once_with(folder="game_data_dir/...")
        self.mock_delete_folder.assert_called_once_with("tnt_local")

    @patch("json.loads")
    def test_branch_guardian_fdu_failure_unresolved(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.return_value = {
            "ScriptCommands": [
                {
                    "Type": "FDU",
                    "Flags": "/FULLUPGRADE /LICENSEE BattlefieldGame /GENERATETYPEDB /SOURCECONTROL /NOSOURCEMODULES /WRITE /SOURCECONTROLOVERWRITE",
                    "Arg1": "",
                    "Arg2": "",
                },
            ]
        }
        self.mock_p4utils.return_value.unresolved.side_effect = [
            [],
            [FilePath("d:/dev/file.txt", "//data/kin/dev/kin-dev/file.txt")],
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        assert self.mock_run_frostbite_data_upgrade.call_count == 0
        assert self.mock_p4utils.return_value.unresolved.call_count == 2

    @patch("elipy2.LOGGER.info")
    @patch("json.loads")
    def test_branch_guardian_loginfo(self, mock_json_loads, mock_logger_info):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "LOGINFO",
                        "Arg1": "A message to the log.",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_logger_info.assert_has_calls(
            [
                call("Branch Guardian info: A message to the log."),
            ],
            any_order=True,
        )

    @patch("json.loads")
    def test_branch_guardian_abortforfiles(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "ABORTFORFILES",
                        "Arg1": "//some/perforce/path/...",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        self.mock_p4utils.return_value.opened.return_value = []
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.opened.assert_called_once_with(
            path="//some/perforce/path/..."
        )

    @patch("json.loads")
    def test_branch_guardian_abortforfiles_failure(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.return_value = {
            "ScriptCommands": [
                {
                    "Type": "ABORTFORFILES",
                    "Arg1": "//some/perforce/path/...",
                },
            ]
        }
        self.mock_p4utils.return_value.opened.return_value = ["some_file.txt"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    @patch("json.loads")
    def test_branch_guardian_revert(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {"Type": "P4REVERT", "Arg1": "//some/perforce/path/...", "Flags": "-w"},
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.revert.assert_has_calls(
            [
                call(
                    path="//some/perforce/path/...",
                    wipe=True,
                )
            ],
            any_order=True,
        )

    @patch("json.loads")
    def test_branch_guardian_revert_dont_wipe(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {"Type": "P4REVERT", "Arg1": "//some/perforce/path/...", "Flags": ""},
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.revert.assert_has_calls(
            [
                call(
                    path="//some/perforce/path/...",
                    wipe=False,
                )
            ],
            any_order=True,
        )

    @patch("json.loads")
    def test_branch_guardian_revert_unsupported_flag(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.return_value = {
            "ScriptCommands": [
                {"Type": "P4REVERT", "Arg1": "//some/perforce/path/...", "Flags": "-x"},
            ]
        }
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    @patch("json.loads")
    def test_branch_guardian_type_not_defined(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.return_value = {
            "ScriptCommands": [
                {
                    "Type": "SOMEOTHERCOMMAND",
                    "Flags": "-UnknownFlag",
                    "Arg1": "UnknownArg1",
                    "Arg2": "UnknownArg2",
                },
            ]
        }
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        assert self.mock_run_frostbite_data_upgrade.call_count == 0

    @patch("json.loads")
    def test_branch_guardian_install_dotnet(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "P4INTEGRATE",
                        "Flags": "-Rbd",
                        "Arg1": "//bf/mainline/trunk-content-dev/bfdata/...",
                        "Arg2": "//bf/mainline/trunk-code-dev/bfdata/...",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
                self.OPTION_USE_PREVIEW_DOTNET_VERSION,
                self.VALUE_USE_PREVIEW_DOTNET_VERSION,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run.assert_has_calls(
            [
                call(
                    [
                        "tnt_root\\Build\\Framework\\bin\\eapm.exe",
                        "install",
                        "DotNetSdk",
                        "-masterconfigfile:masterconfig.xml",
                        "-G:use.dotnet.preview.platform=windows",
                    ],
                ),
            ],
            any_order=True,
        )
        self.mock_find_package.assert_called_once_with(
            "DotNetSdk", framework_args=["-G:use.dotnet.preview.platform=windows"]
        )
        assert (
            os.environ["DOTNET_ROOT"]
            == "D:\\packages\\DotNetSdk\\8.0.201_19773081\\installed\\dotnet"
        )

    def test_submit(self):
        submit_message = (
            f"Upgraded Data from CL#{self.VALUE_CHANGELIST} "
            f"to Code CL#{self.VALUE_CHANGELIST}."
            f"\nIntegrated/copied code, and performed a data upgrade integration."
            f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=False,
            shelve_cl=False,
        )
        assert self.mock_p4utils.return_value.changes_range.call_count == 0

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake"})
    def test_submit_revert_branchid_file(self):
        submit_message = (
            f"Upgraded Data from CL#{self.VALUE_CHANGELIST} "
            f"to Code CL#{self.VALUE_CHANGELIST}."
            f"\nIntegrated/copied code, and performed a data upgrade integration."
            f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_REVERT_BRANCHID_FILE])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=True,
            shelve_cl=False,
        )

    def test_submit_changes_range_default(self):
        submit_message = (
            f"Upgraded Data from CL#[6432, 6543] "
            f"to Code CL#{self.VALUE_CHANGELIST}."
            f"\nIntegrated/copied code, and performed a data upgrade integration."
            f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_LAST_CHANGELIST,
                self.VALUE_LAST_CHANGELIST,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.changes_range.assert_called_once_with(
            self.VALUE_P4_PATH_SOURCE,
            self.VALUE_LAST_CHANGELIST,
            changelist_end=self.VALUE_CHANGELIST,
        )
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=False,
            shelve_cl=False,
        )

    def test_submit_changes_range_empty_string_last_changelist(self):
        submit_message = (
            f"Upgraded Data from CL#{self.VALUE_CHANGELIST} "
            f"to Code CL#{self.VALUE_CHANGELIST}."
            f"\nIntegrated/copied code, and performed a data upgrade integration."
            f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_LAST_CHANGELIST,
                "",
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.changes_range.call_count == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=False,
            shelve_cl=False,
        )

    def test_no_submit(self):
        submit_message = (
            f"Upgraded Data from CL#{self.VALUE_CHANGELIST} "
            f"to Code CL#{self.VALUE_CHANGELIST}."
            f"\nIntegrated/copied code, and performed a data upgrade integration."
            f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NO_SUBMIT])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=False,
            data_upgrade=True,
            revert_branchid_file=False,
            shelve_cl=False,
        )

    def test_submit_shelve_cl(self):
        submit_message = (
            f"Upgraded Data from CL#{self.VALUE_CHANGELIST} "
            f"to Code CL#{self.VALUE_CHANGELIST}."
            f"\nIntegrated/copied code, and performed a data upgrade integration."
            f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_SHELVE_CL, self.VALUE_SHELVE_CL])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=False,
            shelve_cl=True,
        )

    @patch("json.loads")
    def test_submit_branch_guardian(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.side_effect = [
            {
                "ScriptCommands": [
                    {
                        "Type": "P4INTEGRATE",
                        "Flags": "-Rbd",
                        "Arg1": "//bf/mainline/trunk-content-dev/bfdata/...",
                        "Arg2": "//bf/mainline/trunk-code-dev/bfdata/...",
                    },
                ]
            },
            {"Level": "Result", "Message": "Removed 3 rules."},
        ]
        submit_message = (
            f"Upgraded from CL#None to CL#6543."
            f"\nIntegrated/copied code, and used Branch Guardian for data integration and upgrade."
            f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=False,
            shelve_cl=False,
        )

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake", "GAME_ROOT": "game_root"}, clear=True)
    def test_branch_guardian_cleanup_rules(self):
        self.mock_run.side_effect = [
            (0, "some_output", "some_error"),
            (
                0,
                '{"ScriptCommands": [{"Type": "LOGINFO", "Flags": "", "Arg1": "BranchGuardian CLI version 1.3.13", "Arg2": ""}]}',
                "some_error",
            ),
            # (0, "some_output", "some_error"), # see https://gitlab.ea.com/dre-cobra/elipy/elipy-scripts/-/merge_requests/3106
            (
                0,
                [
                    "[",
                    "  {",
                    '    "Level": "Info",',
                    '    "Message": "Some information."',
                    "  },",
                    "  {",
                    '    "Level": "Result",',
                    '    "Message": "Removed 3 rules."',
                    "  }",
                    "]",
                ],
                "some_error",
            ),
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
                self.OPTION_P4_CLIENT_BRANCH_GUARDIAN_RULES_CLEANUP,
                self.VALUE_P4_CLIENT_BRANCH_GUARDIAN_RULES_CLEANUP,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.sync.assert_called_once_with(
            "//bf/admin/branchguardian/rules.guardian.json"
        )
        self.mock_p4utils.return_value.edit.assert_called_once_with(
            "//bf/admin/branchguardian/rules.guardian.json"
        )
        self.mock_run.assert_has_calls(
            [
                call(
                    [
                        "BranchGuardianCLI",
                        "CleanupRules",
                        "-f",
                        "game_root\\rules.guardian.json",
                        "--branch",
                        "//depot/path/source/",
                        "--ignore",
                        "bfdata/Source/BuildTrigger.txt",
                        "--verbose",
                        "true",
                        "--openfiles",
                        "true",
                        "--jsonOutput",
                        "true",
                    ]
                )
            ]
        )
        submit_message = "Auto cleanup: Removed 3 rules." + f"\nJenkins URL: jenkins-url.fake"
        self.mock_p4utils.return_value.submit.assert_called_once_with(
            message=submit_message, revert_unchanged_files=True
        )

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake", "GAME_ROOT": "game_root"}, clear=True)
    @patch("json.loads")
    def test_branch_guardian_cleanup_rules_no_cleanup_workspace(self, mock_json_loads):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.return_value = {
            "ScriptCommands": [
                {
                    "Type": "P4INTEGRATE",
                    "Flags": "-Rbd",
                    "Arg1": "//bf/mainline/trunk-content-dev/bfdata/...",
                    "Arg2": "//bf/mainline/trunk-code-dev/bfdata/...",
                },
            ]
        }
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.edit.call_count == 0

    def test_sync_data_changelist(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.sync.assert_called_once_with(
            path="game_root\\SourceData/...", to_revision=self.VALUE_CHANGELIST
        )

    def test_no_mapping(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.FIXED_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    def test_copy_mapping(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.FIXED_ARGS + [self.OPTION_COPY_MAPPING, self.VALUE_COPY_MAPPING]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.copy_mapping.assert_called_once_with(
            mapping=self.VALUE_COPY_MAPPING, reverse=False, to_revision=self.VALUE_CHANGELIST
        )

    def test_copy_mapping_reverse(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.FIXED_ARGS
            + [self.OPTION_COPY_MAPPING, self.VALUE_COPY_MAPPING, self.OPTION_COPY_REVERSE],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.copy_mapping.assert_called_once_with(
            mapping=self.VALUE_COPY_MAPPING, reverse=True, to_revision=self.VALUE_CHANGELIST
        )

    def test_integrate_mapping(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.integrate.assert_called_once_with(
            mapping=self.VALUE_INTEGRATE_MAPPING,
            reverse=False,
            to_revision=self.VALUE_CHANGELIST,
            ignore_source_history=False,
        )

    def test_integrate_mapping_reverse(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_INTEGRATE_REVERSE])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.integrate.assert_called_once_with(
            mapping=self.VALUE_INTEGRATE_MAPPING,
            reverse=True,
            to_revision=self.VALUE_CHANGELIST,
            ignore_source_history=False,
        )

    def test_cook_data(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory="Data",
            platform="win64",
            clean_avalanche_cook=False,
            pipeline_args=[],
            use_local_code=True,
        )

    def test_cook_data_set_datadir(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform="win64",
            clean_avalanche_cook=False,
            pipeline_args=[],
            use_local_code=True,
        )

    def test_cook_data_other_platform(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATA_PLATFORM, self.VALUE_DATA_PLATFORM]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory="Data",
            platform=self.VALUE_DATA_PLATFORM,
            clean_avalanche_cook=False,
            pipeline_args=[],
            use_local_code=True,
        )

    def test_cook_data_clean(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATA_CLEAN, self.VALUE_DATA_CLEAN]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory="Data",
            platform="win64",
            clean_avalanche_cook=True,
            pipeline_args=[],
            use_local_code=True,
        )

    def test_cook_data(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_PIPELINE_ARGS, self.VALUE_PIPELINE_ARGS]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory="Data",
            platform="win64",
            clean_avalanche_cook=False,
            pipeline_args=[self.VALUE_PIPELINE_ARGS],
            use_local_code=True,
        )

    def test_skip_cook(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_RUN_COOK, self.VALUE_RUN_COOK])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_cook_data.call_count == 0

    def test_local_upgrade(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_LOCAL_UPGRADE])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run.assert_any_call(
            [self.VALUE_LOCAL_UPGRADE_SCRIPT_PATH_DEFAULT], print_std_out=True
        )

    def test_local_upgrade_script_path(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_LOCAL_UPGRADE,
                self.OPTION_LOCAL_UPGRADE_SCRIPT_PATH,
                self.VALUE_LOCAL_UPGRADE_SCRIPT_PATH,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run.assert_any_call([self.VALUE_LOCAL_UPGRADE_SCRIPT_PATH], print_std_out=True)

    def test_local_upgrade_exception(self):
        self.mock_run.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_LOCAL_UPGRADE])
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        assert self.mock_p4utils.return_value.clean.call_count == 0

    def test_local_upgrade_exception_run_clean(self):
        self.mock_run.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_LOCAL_UPGRADE, self.OPTION_P4_CLEAN_FAILED_UPGRADE]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        self.mock_p4utils.return_value.clean.assert_called_once_with(folder="game_data_dir/...")

    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.executable", "executable_path")
    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.resolve")
    def test_dbxmerge_basic(self, mock_dbxmerge_resolve):
        self.mock_p4utils.return_value.unresolved.side_effect = [
            [FilePath("d:/dev/file.dbx", "//data/kin/dev/kin-dev/file.dbx")],
            [],
        ]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        mock_dbxmerge_resolve.assert_called_once_with("d:/dev/file.dbx")

    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.executable", "executable_path")
    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.resolve")
    def test_dbxmerge_no_dbx_files_found(self, mock_dbxmerge_resolve):
        self.mock_p4utils.return_value.unresolved.return_value = []
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        assert mock_dbxmerge_resolve.call_count == 0

    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.executable", "executable_path")
    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.resolve")
    @patch("json.loads")
    def test_dbxmerge_fdu_unresolved(self, mock_json_loads, mock_dbxmerge_resolve):
        self.mock_run.return_value = (0, "some_output", "some_error")
        mock_json_loads.return_value = {
            "ScriptCommands": [
                {
                    "Type": "FDU",
                    "Flags": "/FULLUPGRADE /LICENSEE BattlefieldGame /GENERATETYPEDB /SOURCECONTROL /NOSOURCEMODULES /WRITE /SOURCECONTROLOVERWRITE",
                    "Arg1": "",
                    "Arg2": "",
                },
            ]
        }
        self.mock_p4utils.return_value.unresolved.side_effect = [
            [],  # skip the first invocation of DBXMerge
            [],
            [FilePath("d:/dev/file.dbx", "//data/kin/dev/kin-dev/file.dbx")],
            [],
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_RUN_UPGRADE,
                self.VALUE_RUN_UPGRADE,
                self.OPTION_RUN_BRANCH_GUARDIAN,
                self.VALUE_RUN_BRANCH_GUARDIAN,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
                self.OPTION_P4_PATH_TARGET,
                self.VALUE_P4_PATH_TARGET,
            ],
        )

        assert result.exit_code == 0
        mock_dbxmerge_resolve.assert_called_with("d:/dev/file.dbx")
        self.mock_run_frostbite_data_upgrade.assert_called_once()
