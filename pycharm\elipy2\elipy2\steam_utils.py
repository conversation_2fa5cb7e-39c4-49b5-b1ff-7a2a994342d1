"""
steam_utils.py

Module for interacting with Steam SDK
"""
import base64
import os
import subprocess
from pathlib import Path

from elipy2 import LOGGER, local_paths, secrets, SETTINGS
from elipy2.artifactory_client import ArtifactoryClient
from elipy2.exceptions import ELIPYException

KNOWN_FAILURE_CAUSES = {
    "ERROR (Invalid Password)": "Steam login failed probably due to a malformed steamcmd command.",
    "ERROR (Rate Limit Exceeded)": "Steam public API rate limit exceeded. ",
}


# pylint: disable=logging-fstring-interpolation
class SteamUtils:
    """
    Helper methods for interacting with Steam SDK
    """

    @staticmethod
    def download_steam_sdk(clean_steam_sdk: bool = False) -> str:
        """
        Downloads Steam SDK from Artifactory if it's not on the file system. Retrieves the
        Artifactory repository path and Artifactory credentials from the elipy config.

        :param clean_steam_sdk: Set to True to force download and replace Steam SDK on the file
         system even when it already exists
        :return: The path to Steam SDK on the file system
        """
        LOGGER.info("Downloading Steam SDK...")
        artifactory_steam_sdk_source_path = SETTINGS.get("steam_sdk_repository_path")
        target_path = os.path.join(local_paths.get_packages_path(), "SteamSDK")
        secrets_dict = secrets.get_secrets_from_ess({"build_type": "artifactory"})
        artifactory_user = secrets_dict["username"]
        artifactory_apikey = secrets_dict["reference_token"]
        artifactory_client = ArtifactoryClient(artifactory_user, artifactory_apikey)
        LOGGER.info("steam sdk target_path: %s", target_path)
        download_directory = artifactory_client.download_artifact(
            artifactory_steam_sdk_source_path,
            target_path,
            force_download=clean_steam_sdk,
            unzip_if_archive=True,
        )
        return os.path.normpath(download_directory)

    @staticmethod
    def prepare_steam_user_session(
        steam_config_name: str, steam_sdk_root: str, location="default"
    ) -> str:
        """
        Base 64 encoded steam user session token and writes it to a config.vdf
        file at the standard steam location.

        :param ess_secret_dict: dict containing the steam username
               and config_vdf_base64 value to retrieve
        :param steam_sdk_root: The path to Steam SDK on the file system
        :return: the steam account name (e.g. 'DRE_SVC_STEAM05')
        """
        LOGGER.info("Retrieving Steam Credentials from ESS")

        for ess_path, ess_secret_dict in secrets.get_secrets(
            {"build_type": "steam"}, location
        ).items():
            if ess_path.endswith(steam_config_name):
                steam_account = ess_secret_dict.get("steam_account")
                config_vdf_base64_value = ess_secret_dict.get("steam_config")
                break

        # Put config.vdf inside a config folder that is located in the same directory
        # as the steamcmd.exe file. It's a relative path requirement expected by SteamCMD.

        target_config_path = (
            Path(steam_sdk_root)
            / "installed"
            / "sdk"
            / "tools"
            / "ContentBuilder"
            / "builder"
            / "config"
            / "config.vdf"
        )

        os.makedirs(os.path.dirname(target_config_path), exist_ok=True)
        LOGGER.info("Saving Steam config to file %s", target_config_path)
        with open(target_config_path, "wb") as config:
            config.write(base64.b64decode(config_vdf_base64_value))
            LOGGER.info("Steam config created.")

        return steam_account

    @staticmethod
    def get_steam_user_mapping() -> dict:
        """
        Retrieves the Steam user mapping from the configuration file.

        This function reads the Steam user mapping configuration file and returns
        the mapping as a dictionary. The mapping is expected to contain information
        about Steam users, their associated configurations, and how they are grouped.

        Returns:
            dict: A dictionary containing the Steam user mapping.
        """
        steam_user_mapping_filename = SETTINGS.get("steam_user_mapping_config_path")
        elipy_scripts_yml_dir = str(
            Path(local_paths.get_dev_path())
            / "Python"
            / "virtual"
            / "Lib"
            / "site-packages"
            / "dice_elipy_scripts"
            / "yml"
        )
        steam_user_mapping_filepath = os.path.join(
            os.path.normpath(elipy_scripts_yml_dir),
            steam_user_mapping_filename,
        )
        steam_user_mapping_abspath = os.path.abspath(steam_user_mapping_filepath)
        return SETTINGS.load_auxiliary_file_contents(steam_user_mapping_abspath)

    @staticmethod
    def get_ess_steam_config_name(
        stream, config, region, job_grouping, mapping="unique_users"
    ) -> str:
        """
        Retrieve the Steam user from the configuration for a given stream, region, and config.

        This function looks up the Steam user and associated configuration (config_vdf_base64)
        based on the provided stream, region, config, and job_grouping. The job_grouping
        parameter is used to ensure that no two jobs with the same stream, region, and config
        (such as 'frosty' and 'patch_frosty') can run concurrently, although it is not strictly
        required to mirror the matrices in dst-ci.

        Note: In tests, using a single user with multiple config_vdf_base64 values is a SPOF
        as rate limits are applied per user if exceeded steam rejects logins by that user
        for a period of time.

        Parameters:
            stream (str): The stream name (e.g., 'ch1-content-dev').
            region (str): The region (e.g., 'ww', 'bflabs').
            config (str): The build config (e.g., 'retail', 'final', 'performance').
            job_grouping (str): The job grouping (e.g., 'frosty', 'patch_frosty', 'steam_upload').
            mapping (dict): The Steam user mapping file can have multiple root objects:
                - 'unique_accounts' (default): Uses a unique user for each job, each referencing
                  its own unique config_vdf_base64.

        Returns:
            str: ESS secret name - for example, 'DRE_SVC_STEAM01' which corresponds to
            '/cobra/automation/services/steam_accounts/DRE_SVC_STEAM01'
        """
        steam_user_mapping = SteamUtils.get_steam_user_mapping()

        if steam_user_mapping is None:
            LOGGER.error("No 'steam_account_map' found in configuration.")
            raise KeyError("'steam_account_map' not found in configuration.")
        try:
            LOGGER.info(
                f"Retrieving Steam user for {stream}/{region}/{config}/{job_grouping} job..."
            )
            steam_users = steam_user_mapping[mapping][stream][region][config][job_grouping]
            if len(steam_users) != 1:
                raise ELIPYException(
                    (
                        f"Expected exactly one Steam user for '{stream}/{region}/{config}/"
                        f"{job_grouping}' job, but found {len(steam_users)} users."
                    )
                )

            user = steam_users[0]
            LOGGER.info(
                (
                    f"Steam user '{user} retrieved from {mapping} for this "
                    f"'{stream}/{region}/{config}/{job_grouping}' job. "
                )
            )
            return user
        except KeyError as exc:
            LOGGER.error(
                (f"Could not find Steam user for '{stream}/{region}/{config}/{job_grouping}' job.")
            )
            raise

    @staticmethod
    def validate_session_for_steam_account(steam_sdk_root: str, steam_account: str):
        """
        Validates a Steam session by logging in with the specified account.

        Credentials are retrieved using the config.vdf file written by the
        write_steam_user_session_token method, so no password is required.
        Do not pass a password to this function, as command logging would expose it.

        :param steam_sdk_root: Path to the Steam SDK on the file system.
        :param steam_account: Steam account username to log in with (not the ESS secret name).
        :raises ELIPYException: If steamcmd is not found, login fails, or the command times out.
        :side effects: Logs into Steam and validates the session.
        """

        steam_cmd_executable = os.path.normpath(
            os.path.join(
                steam_sdk_root,
                "installed/sdk/tools/ContentBuilder/builder/steamcmd.exe",
            )
        )
        if not os.path.isfile(steam_cmd_executable):
            raise ELIPYException(f"steamcmd executable not found at {steam_cmd_executable}")

        expected_config_vdf = os.path.join(
            os.path.dirname(steam_cmd_executable), "config", "config.vdf"
        )
        LOGGER.info(
            f"Logging into Steam | Account: {steam_account} | SDK Root: {steam_sdk_root} | "
            f"steamcmd: {steam_cmd_executable} | expected config.vdf: {expected_config_vdf}"
        )
        steam_cmd = [steam_cmd_executable, "+login", steam_account, "+quit"]
        LOGGER.debug("Running command: %s", " ".join(steam_cmd))

        try:
            result = subprocess.run(
                steam_cmd,
                capture_output=True,
                text=True,
                timeout=30,
                check=True,
            )
            output = result.stdout + result.stderr
            LOGGER.debug("steamcmd output: %s", output)
            LOGGER.debug("steamcmd return code: %d", result.returncode)

            for failure_cause, exception_msg in KNOWN_FAILURE_CAUSES.items():
                if failure_cause in output:
                    raise ELIPYException(exception_msg)

            LOGGER.info("Steamcmd login successful!")

        except subprocess.TimeoutExpired:
            raise ELIPYException("Steam login command timed out.")
        except subprocess.CalledProcessError as exc:
            LOGGER.error("steamcmd returned non-zero exit code: %s", exc)
            raise ELIPYException(
                f"Steamcmd returned a non-zero exit code. " f"Failed with error: {exc}"
            ) from exc
        except Exception as exc:
            LOGGER.error("Error executing Steam SDK: %s", exc)
            raise ELIPYException(str(exc)) from exc
