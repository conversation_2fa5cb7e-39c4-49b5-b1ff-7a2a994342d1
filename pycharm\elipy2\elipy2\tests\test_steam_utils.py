import os
from pathlib import Path

import pytest
from mock import <PERSON><PERSON>ock, mock_open, patch

from elipy2.steam_utils import SteamUtils


@patch("elipy2.steam_utils.os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
class TestSteamUtils(object):
    @patch("elipy2.local_paths.get_packages_path", MagicMock(return_value="D:\\packages"))
    @patch("elipy2.SETTINGS.get", MagicMock(return_value="my\\mock\\path"))
    @patch(
        "elipy2.secrets.get_secrets_from_ess",
        MagicMock(return_value={"username": "<EMAIL>", "reference_token": "<PASSWORD>"}),
    )
    @patch("elipy2.steam_utils.ArtifactoryClient")
    def test_download_steam_sdk(self, mock_artifactory_client_class):
        mock_artifactory_client_instance = MagicMock()
        mock_artifactory_client_instance.download_artifact.return_value = "D:/packages/SteamSDK/123"
        mock_artifactory_client_class.return_value = mock_artifactory_client_instance

        download_directory = SteamUtils.download_steam_sdk()
        assert download_directory == os.path.normpath("D:/packages/SteamSDK/123")
        mock_artifactory_client_instance.download_artifact.assert_called_once_with(
            "my\\mock\\path",
            "D:\\packages\\SteamSDK",
            force_download=False,
            unzip_if_archive=True,
        )

    @patch("elipy2.steam_utils.os.makedirs", MagicMock())
    @patch("elipy2.steam_utils.base64.b64decode", MagicMock(side_effect=lambda arg: arg))
    @patch("elipy2.steam_utils.open", new_callable=mock_open)
    @patch("elipy2.secrets.get_secrets")
    def test_prepare_steam_user_session(self, mock_get_secrets, open_mock):
        steam_config_name = "steam_config_name.yml"
        sdk_root = "D:/packages/SteamSDK/version"
        steam_account = "steam_account"
        config_vdf_base64_value = b"base64EncodedString"
        mock_get_secrets.return_value = {
            "some_path/"
            + steam_config_name: {
                "steam_account": steam_account,
                "steam_config": config_vdf_base64_value,
            }
        }
        result = SteamUtils.prepare_steam_user_session(steam_config_name, sdk_root)
        assert result == steam_account
        assert open_mock.call_count == 1

        expeced_target_config_path = (
            Path(sdk_root)
            / "installed"
            / "sdk"
            / "tools"
            / "ContentBuilder"
            / "builder"
            / "config"
            / "config.vdf"
        )

        open_mock.assert_called_with(expeced_target_config_path, "wb")
        handle = open_mock()
        handle.write.assert_called_once_with(config_vdf_base64_value)

    @patch("elipy2.steam_utils.SETTINGS.get", MagicMock(return_value="steam_user_mapping.yml"))
    @patch("elipy2.steam_utils.SETTINGS.load_auxiliary_file_contents")
    def test_get_steam_user_mapping(self, mock_load_aux):
        mock_load_aux.return_value = {"unique_sessions": {}}
        result = SteamUtils.get_steam_user_mapping()
        assert result == {"unique_sessions": {}}
        mock_load_aux.assert_called_once()

    @pytest.mark.parametrize(
        "steam_account_map,stream,region,config,job_grouping,mapping,expected_users",
        [
            (
                {
                    "unique_sessions": {
                        "ch1-content-dev": {
                            "ww": {
                                "final": {
                                    "frosty": ["test-user-1"],
                                    "patch_frosty": ["test-user-1"],
                                    "upload": ["test-user-1"],
                                }
                            }
                        }
                    }
                },
                "ch1-content-dev",
                "ww",
                "final",
                "frosty",
                "unique_sessions",
                "test-user-1",
            ),
            (
                {
                    "unique_accounts": {
                        "ch1-content-dev": {
                            "ww": {
                                "final": {
                                    "frosty": ["test-user-1"],
                                    "patch_frosty": ["test-user-2"],
                                    "upload": ["test-user-3"],
                                }
                            }
                        }
                    }
                },
                "ch1-content-dev",
                "ww",
                "final",
                "patch_frosty",
                "unique_accounts",
                "test-user-2",
            ),
        ],
    )
    def test_get_ess_steam_config_name_valid(
        self,
        steam_account_map,
        stream,
        region,
        config,
        job_grouping,
        mapping,
        expected_users,
    ):
        with patch(
            "elipy2.steam_utils.SETTINGS.get",
            MagicMock(return_value="steam_user_mapping.yml"),
        ), patch(
            "elipy2.steam_utils.SETTINGS.load_auxiliary_file_contents",
            MagicMock(return_value=steam_account_map),
        ), patch(
            "os.path.dirname",
            MagicMock(return_value="/fake/site-packages/dice_elipy_scripts"),
        ):
            su = SteamUtils()
            users = su.get_ess_steam_config_name(stream, config, region, job_grouping, mapping)
            assert users == expected_users

    @pytest.mark.parametrize(
        "steam_account_map,stream,region,config,job_grouping,mapping,missing_key",
        [
            # Missing stream
            (
                {"unique_sessions": {}},
                "missing-stream",
                "ww",
                "final",
                "frosty",
                "unique_sessions",
                "missing-stream",
            ),
            # Missing region
            (
                {"unique_sessions": {"ch1-content-dev": {}}},
                "ch1-content-dev",
                "missing-region",
                "final",
                "frosty",
                "unique_sessions",
                "missing-region",
            ),
            # Missing config
            (
                {"unique_sessions": {"ch1-content-dev": {"ww": {}}}},
                "ch1-content-dev",
                "ww",
                "missing-config",
                "frosty",
                "unique_sessions",
                "missing-config",
            ),
            # Missing job_grouping
            (
                {"unique_sessions": {"ch1-content-dev": {"ww": {"final": {}}}}},
                "ch1-content-dev",
                "ww",
                "final",
                "missing-job",
                "unique_sessions",
                "missing-job",
            ),
        ],
    )
    def test_get_ess_steam_config_name_missing_key(
        self, steam_account_map, stream, region, config, job_grouping, mapping, missing_key
    ):
        with patch(
            "elipy2.steam_utils.SETTINGS.get",
            MagicMock(
                side_effect=lambda key: "dummy_path"
                if key == "steam_user_mapping_config_path"
                else None
            ),
        ), patch(
            "elipy2.steam_utils.SETTINGS.load_auxiliary_file_contents",
            MagicMock(return_value=steam_account_map),
        ):
            su = SteamUtils()
            with pytest.raises(KeyError):
                su.get_ess_steam_config_name(stream, config, region, job_grouping, mapping)

    @patch("elipy2.steam_utils.os.path.isfile", MagicMock(return_value=True))
    @patch("elipy2.steam_utils.subprocess.run")
    def test_validate_session_for_steam_account_success(self, mock_run):
        mock_result = MagicMock()
        mock_result.stdout = "Success"
        mock_result.stderr = ""
        mock_result.returncode = 0
        mock_run.return_value = mock_result
        SteamUtils.validate_session_for_steam_account("/sdk/root", "steamuser")
        mock_run.assert_called_once()

    @patch("elipy2.steam_utils.os.path.isfile", MagicMock(return_value=False))
    def test_validate_session_for_steam_account_missing_exe(self):
        with pytest.raises(Exception):
            SteamUtils.validate_session_for_steam_account("/sdk/root", "steamuser")

    @patch("elipy2.steam_utils.os.path.isfile", MagicMock(return_value=True))
    @patch("elipy2.steam_utils.subprocess.run", side_effect=Exception("fail"))
    def test_validate_session_for_steam_account_exception(self, mock_run):
        with pytest.raises(Exception):
            SteamUtils.validate_session_for_steam_account("/sdk/root", "steamuser")
