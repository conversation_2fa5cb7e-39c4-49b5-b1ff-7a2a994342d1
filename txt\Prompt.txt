
Got the below error from CH1-content-dev-first-patch.combined_bundles.ps5.24633996.24633996.24621111.24621111
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: Creating delta bundles from combined bundles
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: Using disc baseline for first patch: CH1-content-dev@24580360, CH1-content-dev-disc-build@24580360
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: Copying baseline head bundles from \\filer.dice.ad.ea.com\builds\Battlefield\baselines\BattlefieldGame\CH1-content-dev-disc-build\24580360\CH1-content-dev\24580360\ps5\bundles\head to D:\dev\TnT\local\combine_bundles\base
17:58:01 2025-07-31 16:58:01 elipy2 [ERROR]: Source does not exist, cannot run Robocopy: \\filer.dice.ad.ea.com\builds\Battlefield\baselines\BattlefieldGame\CH1-content-dev-disc-build\24580360\CH1-content-dev\24580360\ps5\bundles\head
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: setting code area
17:58:01 2025-07-31 16:58:01 elipy2 [WARNING]: Failed to add code area
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2024-1.3
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2024-1.3
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2024-1.3
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2024-1.3
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2024-1.3
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2024-1.3
17:58:01 2025-07-31 16:58:01 elipy2 [INFO]: setting code area
17:58:01 Traceback 
17:58:02   File "D:\dev\Python\virtual\Lib\site-packages\dice_elipy_scripts\combined_bundles.py", line 399, in cli
17:58:02     _filer.fetch_baseline_bundles_head(
17:58:02   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\telemetry.py", line 55, in wrapper
17:58:02     value = method(*args, **kwargs)
17:58:02             ^^^^^^^^^^^^^^^^^^^^^^^
17:58:02   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\filer.py", line 572, in fetch_baseline_bundles_head
17:58:02     core.robocopy(baseline_path, dest, purge=True)
17:58:02   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\telemetry.py", line 55, in wrapper
17:58:02     value = method(*args, **kwargs)
17:58:02             ^^^^^^^^^^^^^^^^^^^^^^^
17:58:02   File "D:\dev\Python\virtual\Lib\site-packages\elipy2\core.py", line 467, in robocopy
17:58:02     raise IOError("Source does not exist, cannot run Robocopy: {}".format(source))
17:58:02 OSError: Source does not exist, cannot run Robocopy: \\filer.dice.ad.ea.com\builds\Battlefield\baselines\BattlefieldGame\CH1-content-dev-disc-build\24580360\CH1-content-dev\24580360\ps5\bundles\head

